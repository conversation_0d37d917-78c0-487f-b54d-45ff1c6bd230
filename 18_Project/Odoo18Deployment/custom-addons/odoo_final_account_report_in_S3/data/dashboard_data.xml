<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Balance Sheet Dashboard -->
        <record id="dashboard_balance_sheet" model="spreadsheet.dashboard">
            <field name="name">Balance Sheet</field>
            <field name="spreadsheet_binary_data" type="base64" file="odoo_final_account_report_in_S3/data/files/balance_sheet_dashboard.json"/>
            <field name="main_data_model_ids" eval="[(4, ref('model_account_final_report'))]"/>
            <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_finance"/>
            <field name="group_ids" eval="[Command.link(ref('account.group_account_readonly')), Command.link(ref('account.group_account_user'))]"/>
            <field name="sequence">30</field>
            <field name="is_published">True</field>
        </record>

        <!-- Profit And Loss Dashboard -->
        <record id="dashboard_profit_loss" model="spreadsheet.dashboard">
            <field name="name">Profit And Loss</field>
            <field name="spreadsheet_binary_data" type="base64" file="odoo_final_account_report_in_S3/data/files/profit_loss_dashboard.json"/>
            <field name="main_data_model_ids" eval="[(4, ref('model_account_final_report'))]"/>
            <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_finance"/>
            <field name="group_ids" eval="[Command.link(ref('account.group_account_readonly')), Command.link(ref('account.group_account_user'))]"/>
            <field name="sequence">31</field>
            <field name="is_published">True</field>
        </record>

        <!-- Dashboard Action for Balance Sheet -->
        <record id="action_balance_sheet_dashboard" model="ir.actions.client">
            <field name="name">Balance Sheet Dashboard</field>
            <field name="tag">action_spreadsheet_dashboard</field>
            <field name="params" eval="{'dashboard_id': ref('dashboard_balance_sheet')}"/>
        </record>

        <!-- Dashboard Action for Profit & Loss -->
        <record id="action_profit_loss_dashboard" model="ir.actions.client">
            <field name="name">Profit And Loss Dashboard</field>
            <field name="tag">action_spreadsheet_dashboard</field>
            <field name="params" eval="{'dashboard_id': ref('dashboard_profit_loss')}"/>
        </record>

        <!-- Sample Data Action -->
        <record id="action_create_sample_data" model="ir.actions.server">
            <field name="name">Create Sample Report Data</field>
            <field name="model_id" ref="model_account_final_report"/>
            <field name="state">code</field>
            <field name="code">
report = model.create_sample_report()
action = {
    'type': 'ir.actions.client',
    'tag': 'display_notification',
    'params': {
        'title': 'Sample Data Created',
        'message': f'Sample report "{report.name}" created successfully for dashboard testing.',
        'type': 'success',
    }
}
            </field>
        </record>
    </data>
</odoo>