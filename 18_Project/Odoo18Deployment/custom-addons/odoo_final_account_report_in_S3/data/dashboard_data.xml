<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Final Account Dashboard -->
        <record id="dashboard_final_account_schedule3" model="spreadsheet.dashboard">
            <field name="name">Schedule 3 Final Account Report</field>
            <field name="spreadsheet_data">{"version": 21, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 6, "rowNumber": 50, "rows": {}, "cols": {}, "merges": [], "cells": {}}], "lists": {}, "pivots": {}, "pivotNextId": 1, "listNextId": 1}</field>
            <field name="spreadsheet_binary_data" type="base64" file="odoo_final_account_report_in_S3/data/files/final_account_dashboard.json"/>
            <field name="main_data_model_ids" eval="[(4, ref('model_account_final_report'))]"/>
            <field name="sample_dashboard_file_path">odoo_final_account_report_in_S3/data/files/final_account_dashboard.json</field>
            <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_finance"/>
            <field name="group_ids" eval="[Command.link(ref('account.group_account_readonly')), Command.link(ref('account.group_account_user'))]"/>
            <field name="sequence">30</field>
            <field name="is_published">True</field>
        </record>

        <!-- Dashboard Action for Final Account Reports -->
        <record id="action_final_account_dashboard" model="ir.actions.client">
            <field name="name">Final Account Dashboard</field>
            <field name="tag">action_spreadsheet_dashboard</field>
            <field name="params" eval="{'dashboard_id': ref('dashboard_final_account_schedule3')}"/>
        </record>

        <!-- Sample Data Action -->
        <record id="action_create_sample_data" model="ir.actions.server">
            <field name="name">Create Sample Report Data</field>
            <field name="model_id" ref="model_account_final_report"/>
            <field name="state">code</field>
            <field name="code">
report = model.create_sample_report()
action = {
    'type': 'ir.actions.client',
    'tag': 'display_notification',
    'params': {
        'title': 'Sample Data Created',
        'message': f'Sample report "{report.name}" created successfully for dashboard testing.',
        'type': 'success',
    }
}
            </field>
        </record>
    </data>
</odoo>