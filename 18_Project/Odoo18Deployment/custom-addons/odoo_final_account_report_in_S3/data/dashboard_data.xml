<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Final Account Dashboard -->
        <record id="dashboard_final_account_schedule3" model="spreadsheet.dashboard">
            <field name="name">Schedule 3 Final Account Report</field>
            <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_finance"/>
            <field name="sequence">30</field>
            <field name="is_published">True</field>
            <field name="group_ids" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="main_data_model_ids" eval="[(4, ref('model_account_final_report'))]"/>
            <field name="spreadsheet_binary_data" type="base64" file="odoo_final_account_report_in_S3/data/files/final_account_dashboard.json"/>
        </record>

        <!-- Dashboard Action for Final Account Reports -->
        <record id="action_final_account_dashboard" model="ir.actions.client">
            <field name="name">Final Account Dashboard</field>
            <field name="tag">action_spreadsheet_dashboard</field>
            <field name="params" eval="{'dashboard_id': ref('dashboard_final_account_schedule3')}"/>
        </record>
    </data>
</odoo>