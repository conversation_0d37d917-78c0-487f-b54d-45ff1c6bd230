<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Dashboard Group for Final Account Reports -->
        <record id="dashboard_group_final_account" model="spreadsheet.dashboard.group">
            <field name="name">Final Account Reports</field>
            <field name="sequence">15</field>
        </record>

        <!-- Final Account Dashboard -->
        <record id="dashboard_final_account_schedule3" model="spreadsheet.dashboard">
            <field name="name">Schedule 3 Final Account Report</field>
            <field name="dashboard_group_id" ref="dashboard_group_final_account"/>
            <field name="sequence">10</field>
            <field name="is_published">True</field>
            <field name="group_ids" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="spreadsheet_data" eval="open('odoo_final_account_report_in_S3/data/files/final_account_dashboard.json', 'rb').read()"/>
        </record>

        <!-- Dashboard Action for Final Account Reports -->
        <record id="action_final_account_dashboard" model="ir.actions.client">
            <field name="name">Final Account Dashboard</field>
            <field name="tag">action_spreadsheet_dashboard</field>
            <field name="params" eval="{'dashboard_id': ref('dashboard_final_account_schedule3')}"/>
        </record>

    </data>
</odoo>