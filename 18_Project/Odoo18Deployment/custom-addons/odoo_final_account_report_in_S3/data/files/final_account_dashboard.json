{"version": 21, "sheets": [{"id": "final-account-balance-sheet", "name": "Balance Sheet", "colNumber": 6, "rowNumber": 50, "rows": {"1": {"size": 30}, "2": {"size": 25}, "3": {"size": 20}, "5": {"size": 25}, "15": {"size": 25}, "25": {"size": 25}}, "cols": {"0": {"size": 250}, "1": {"size": 120}, "2": {"size": 120}, "3": {"size": 120}, "4": {"size": 120}, "5": {"size": 120}}, "merges": ["A1:F1", "A2:F2", "A3:F3"], "cells": {"A1": {"content": "FINAL ACCOUNT REPORT - SCHEDULE 3", "style": {"fontSize": 16, "bold": true, "align": "center", "fillColor": "#D9E1F2"}}, "A2": {"content": "BALANCE SHEET", "style": {"fontSize": 14, "bold": true, "align": "center", "fillColor": "#B4C6E7"}}, "A3": {"content": "As at [DATE]", "style": {"fontSize": 12, "align": "center", "fillColor": "#E2EFDA"}}, "A5": {"content": "Particulars", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "B5": {"content": "Opening Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "C5": {"content": "Period Movement", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "D5": {"content": "Closing Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "A7": {"content": "ASSETS", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A8": {"content": "Current Assets:"}, "A9": {"content": "  Receivable"}, "B9": {"content": "=ODOO.LIST(1,1,\"opening_balance\")", "format": "#,##0.00"}, "C9": {"content": "=ODOO.LIST(1,1,\"period_balance\")", "format": "#,##0.00"}, "D9": {"content": "=ODOO.LIST(1,1,\"balance\")", "format": "#,##0.00"}, "A10": {"content": "  Bank and Cash"}, "B10": {"content": "=ODOO.LIST(1,2,\"opening_balance\")", "format": "#,##0.00"}, "C10": {"content": "=ODOO.LIST(1,2,\"period_balance\")", "format": "#,##0.00"}, "D10": {"content": "=ODOO.LIST(1,2,\"balance\")", "format": "#,##0.00"}, "A11": {"content": "  Current Assets"}, "B11": {"content": "=ODOO.LIST(1,3,\"opening_balance\")", "format": "#,##0.00"}, "C11": {"content": "=ODOO.LIST(1,3,\"period_balance\")", "format": "#,##0.00"}, "D11": {"content": "=ODOO.LIST(1,3,\"balance\")", "format": "#,##0.00"}, "A13": {"content": "Non-Current Assets:"}, "A14": {"content": "  Fixed Assets"}, "B14": {"content": "=ODOO.LIST(1,4,\"opening_balance\")", "format": "#,##0.00"}, "C14": {"content": "=ODOO.LIST(1,4,\"period_balance\")", "format": "#,##0.00"}, "D14": {"content": "=ODOO.LIST(1,4,\"balance\")", "format": "#,##0.00"}, "A15": {"content": "  Non-current Assets"}, "B15": {"content": "=ODOO.LIST(1,5,\"opening_balance\")", "format": "#,##0.00"}, "C15": {"content": "=ODOO.LIST(1,5,\"period_balance\")", "format": "#,##0.00"}, "D15": {"content": "=ODOO.LIST(1,5,\"balance\")", "format": "#,##0.00"}, "A17": {"content": "TOTAL ASSETS", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "B17": {"content": "=PIVOT.VALUE(1,\"total_assets\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}, "D17": {"content": "=PIVOT.VALUE(1,\"total_assets\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}, "A20": {"content": "LIABILITIES AND EQUITY", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A21": {"content": "Current Liabilities:"}, "A22": {"content": "  Payable"}, "B22": {"content": "=ODOO.LIST(2,1,\"opening_balance\")", "format": "#,##0.00"}, "C22": {"content": "=ODOO.LIST(2,1,\"period_balance\")", "format": "#,##0.00"}, "D22": {"content": "=ODOO.LIST(2,1,\"balance\")", "format": "#,##0.00"}, "A23": {"content": "  Current Liabilities"}, "B23": {"content": "=ODOO.LIST(2,2,\"opening_balance\")", "format": "#,##0.00"}, "C23": {"content": "=ODOO.LIST(2,2,\"period_balance\")", "format": "#,##0.00"}, "D23": {"content": "=ODOO.LIST(2,2,\"balance\")", "format": "#,##0.00"}, "A25": {"content": "Equity:"}, "A26": {"content": "  Equity"}, "B26": {"content": "=ODOO.LIST(3,1,\"opening_balance\")", "format": "#,##0.00"}, "C26": {"content": "=ODOO.LIST(3,1,\"period_balance\")", "format": "#,##0.00"}, "D26": {"content": "=ODOO.LIST(3,1,\"balance\")", "format": "#,##0.00"}, "A27": {"content": "  Current Year Earnings"}, "B27": {"content": "=ODOO.LIST(3,2,\"opening_balance\")", "format": "#,##0.00"}, "C27": {"content": "=ODOO.LIST(3,2,\"period_balance\")", "format": "#,##0.00"}, "D27": {"content": "=ODOO.LIST(3,2,\"balance\")", "format": "#,##0.00"}, "A29": {"content": "TOTAL LIABILITIES AND EQUITY", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "B29": {"content": "=PIVOT.VALUE(1,\"total_liabilities\")+PIVOT.VALUE(1,\"total_equity\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}, "D29": {"content": "=PIVOT.VALUE(1,\"total_liabilities\")+PIVOT.VALUE(1,\"total_equity\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}}}, {"id": "final-account-profit-loss", "name": "Profit & Loss", "colNumber": 6, "rowNumber": 40, "rows": {"1": {"size": 30}, "2": {"size": 25}, "3": {"size": 20}}, "cols": {"0": {"size": 250}, "1": {"size": 120}, "2": {"size": 120}, "3": {"size": 120}, "4": {"size": 120}, "5": {"size": 120}}, "merges": ["A1:F1", "A2:F2", "A3:F3"], "cells": {"A1": {"content": "PROFIT & LOSS STATEMENT", "style": {"fontSize": 16, "bold": true, "align": "center", "fillColor": "#D9E1F2"}}, "A2": {"content": "For the period [DATE_FROM] to [DATE_TO]", "style": {"fontSize": 12, "align": "center", "fillColor": "#E2EFDA"}}, "A5": {"content": "Particulars", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "B5": {"content": "Opening Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "C5": {"content": "Period Movement", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "D5": {"content": "Closing Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "A7": {"content": "REVENUE", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A8": {"content": "  Income"}, "B8": {"content": "=ODOO.LIST(4,1,\"opening_balance\")", "format": "#,##0.00"}, "C8": {"content": "=ODOO.LIST(4,1,\"period_balance\")", "format": "#,##0.00"}, "D8": {"content": "=ODOO.LIST(4,1,\"balance\")", "format": "#,##0.00"}, "A9": {"content": "  Other Income"}, "B9": {"content": "=ODOO.LIST(4,2,\"opening_balance\")", "format": "#,##0.00"}, "C9": {"content": "=ODOO.LIST(4,2,\"period_balance\")", "format": "#,##0.00"}, "D9": {"content": "=ODOO.LIST(4,2,\"balance\")", "format": "#,##0.00"}, "A11": {"content": "TOTAL REVENUE", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "D11": {"content": "=PIVOT.VALUE(1,\"total_income\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}, "A14": {"content": "EXPENSES", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A15": {"content": "  Expenses"}, "B15": {"content": "=ODOO.LIST(5,1,\"opening_balance\")", "format": "#,##0.00"}, "C15": {"content": "=ODOO.LIST(5,1,\"period_balance\")", "format": "#,##0.00"}, "D15": {"content": "=ODOO.LIST(5,1,\"balance\")", "format": "#,##0.00"}, "A16": {"content": "  Depreciation"}, "B16": {"content": "=ODOO.LIST(5,2,\"opening_balance\")", "format": "#,##0.00"}, "C16": {"content": "=ODOO.LIST(5,2,\"period_balance\")", "format": "#,##0.00"}, "D16": {"content": "=ODOO.LIST(5,2,\"balance\")", "format": "#,##0.00"}, "A17": {"content": "  Cost of Revenue"}, "B17": {"content": "=ODOO.LIST(5,3,\"opening_balance\")", "format": "#,##0.00"}, "C17": {"content": "=ODOO.LIST(5,3,\"period_balance\")", "format": "#,##0.00"}, "D17": {"content": "=ODOO.LIST(5,3,\"balance\")", "format": "#,##0.00"}, "A19": {"content": "TOTAL EXPENSES", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "D19": {"content": "=PIVOT.VALUE(1,\"total_expenses\")", "style": {"bold": true, "fillColor": "#E2EFDA"}, "format": "#,##0.00"}, "A22": {"content": "NET PROFIT/(LOSS)", "style": {"bold": true, "fillColor": "#FFE699"}}, "D22": {"content": "=PIVOT.VALUE(1,\"net_profit_loss\")", "style": {"bold": true, "fillColor": "#FFE699"}, "format": "#,##0.00"}}}], "lists": {"1": {"id": "1", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "assets"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "context": {}}, "2": {"id": "2", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "liabilities"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "context": {}}, "3": {"id": "3", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "equity"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "context": {}}, "4": {"id": "4", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "income"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "context": {}}, "5": {"id": "5", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "expenses"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "context": {}}}, "pivots": {"1": {"type": "ODOO", "cache": {}, "definition": {"metaData": {"colGroupBys": [], "rowGroupBys": [], "activeMeasures": ["total_assets", "total_liabilities", "total_equity", "total_income", "total_expenses", "net_profit_loss"], "resModel": "account.final.report", "fields": {"total_assets": {"string": "Total Assets", "type": "monetary"}, "total_liabilities": {"string": "Total Liabilities", "type": "monetary"}, "total_equity": {"string": "Total Equity", "type": "monetary"}, "total_income": {"string": "Total Income", "type": "monetary"}, "total_expenses": {"string": "Total Expenses", "type": "monetary"}, "net_profit_loss": {"string": "Net Profit/Loss", "type": "monetary"}}}, "searchParams": {"domain": [["state", "=", "generated"]], "context": {}, "groupBy": [], "orderBy": []}}, "id": "1", "measures": [{"id": "total_assets", "fieldName": "total_assets"}, {"id": "total_liabilities", "fieldName": "total_liabilities"}, {"id": "total_equity", "fieldName": "total_equity"}, {"id": "total_income", "fieldName": "total_income"}, {"id": "total_expenses", "fieldName": "total_expenses"}, {"id": "net_profit_loss", "fieldName": "net_profit_loss"}], "model": "account.final.report", "name": "Final Account Totals", "sortedColumn": null, "formulaId": "1", "columns": [], "rows": []}}, "pivotNextId": 2, "listNextId": 6}