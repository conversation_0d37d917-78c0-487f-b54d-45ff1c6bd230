{"version": 21, "sheets": [{"id": "final-account-balance-sheet", "name": "Balance Sheet", "colNumber": 6, "rowNumber": 50, "rows": {"1": {"size": 30}, "2": {"size": 25}, "3": {"size": 20}, "5": {"size": 25}, "15": {"size": 25}, "25": {"size": 25}}, "cols": {"0": {"size": 250}, "1": {"size": 120}, "2": {"size": 120}, "3": {"size": 120}, "4": {"size": 120}, "5": {"size": 120}}, "merges": ["A1:F1", "A2:F2", "A3:F3"], "cells": {"A1": {"content": "FINAL ACCOUNT REPORT - SCHEDULE 3", "style": {"fontSize": 16, "bold": true, "align": "center", "fillColor": "#D9E1F2"}}, "A2": {"content": "BALANCE SHEET", "style": {"fontSize": 14, "bold": true, "align": "center", "fillColor": "#B4C6E7"}}, "A3": {"content": "As at [DATE]", "style": {"fontSize": 12, "align": "center", "fillColor": "#E2EFDA"}}, "A5": {"content": "Particulars", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "B5": {"content": "Opening Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "C5": {"content": "Period Movement", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "D5": {"content": "Closing Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "A7": {"content": "ASSETS", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A8": {"content": "Current Assets:"}, "A9": {"content": "  Receivable"}, "A10": {"content": "  Bank and Cash"}, "A11": {"content": "  Current Assets"}, "A13": {"content": "Non-Current Assets:"}, "A14": {"content": "  Fixed Assets"}, "A15": {"content": "  Non-current Assets"}, "A17": {"content": "TOTAL ASSETS", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A20": {"content": "LIABILITIES AND EQUITY", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A21": {"content": "Current Liabilities:"}, "A22": {"content": "  Payable"}, "A23": {"content": "  Current Liabilities"}, "A25": {"content": "Equity:"}, "A26": {"content": "  Equity"}, "A27": {"content": "  Current Year Earnings"}, "A29": {"content": "TOTAL LIABILITIES AND EQUITY", "style": {"bold": true, "fillColor": "#E2EFDA"}}}}, {"id": "final-account-profit-loss", "name": "Profit & Loss", "colNumber": 6, "rowNumber": 40, "rows": {"1": {"size": 30}, "2": {"size": 25}, "3": {"size": 20}}, "cols": {"0": {"size": 250}, "1": {"size": 120}, "2": {"size": 120}, "3": {"size": 120}, "4": {"size": 120}, "5": {"size": 120}}, "merges": ["A1:F1", "A2:F2", "A3:F3"], "cells": {"A1": {"content": "PROFIT & LOSS STATEMENT", "style": {"fontSize": 16, "bold": true, "align": "center", "fillColor": "#D9E1F2"}}, "A2": {"content": "For the period [DATE_FROM] to [DATE_TO]", "style": {"fontSize": 12, "align": "center", "fillColor": "#E2EFDA"}}, "A5": {"content": "Particulars", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "B5": {"content": "Opening Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "C5": {"content": "Period Movement", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "D5": {"content": "Closing Balance", "style": {"bold": true, "fillColor": "#F2F2F2"}}, "A7": {"content": "REVENUE", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A8": {"content": "  Income"}, "A9": {"content": "  Other Income"}, "A11": {"content": "TOTAL REVENUE", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A14": {"content": "EXPENSES", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A15": {"content": "  Expenses"}, "A16": {"content": "  Depreciation"}, "A17": {"content": "  Cost of Revenue"}, "A19": {"content": "TOTAL EXPENSES", "style": {"bold": true, "fillColor": "#E2EFDA"}}, "A22": {"content": "NET PROFIT/(LOSS)", "style": {"bold": true, "fillColor": "#FFE699"}}}}]}