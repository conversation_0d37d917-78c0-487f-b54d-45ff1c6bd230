<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Final Account Report Form View -->
        <record id="view_account_final_report_form" model="ir.ui.view">
            <field name="name">account.final.report.form</field>
            <field name="model">account.final.report</field>
            <field name="arch" type="xml">
                <form string="Final Account Report">
                    <header>
                        <button name="action_generate_report" type="object" string="Generate Report"
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="validate_report_data" type="object" string="Validate Data"
                                class="btn-secondary" invisible="state == 'draft'"/>
                        <button name="action_export_excel" type="object" string="Export to Excel"
                                class="btn-secondary" invisible="state == 'draft'"
                                help="Export report to Excel format following Schedule 3 layout"/>
                        <button name="action_transfer_pl" type="object" string="Transfer P&amp;L"
                                class="btn-secondary" invisible="state == 'draft'"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,generated,exported"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="state != 'draft'"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="company_id" readonly="state != 'draft'"/>
                                <field name="date_from" readonly="state != 'draft'"/>
                                <field name="date_to" readonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="include_unposted" readonly="state != 'draft'"/>
                                <field name="pl_transfer_mode" readonly="state != 'draft'"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                        </group>
                        
                        <notebook invisible="state == 'draft'">
                            <page string="Summary" name="summary">
                                <group>
                                    <group string="Balance Sheet Totals">
                                        <field name="total_assets" widget="monetary"/>
                                        <field name="total_liabilities" widget="monetary"/>
                                        <field name="total_equity" widget="monetary"/>
                                        <separator/>
                                        <label for="balance_check" string="Balance Check Assets = Liabilities + Equity"/>
                                        <field name="balance_check" widget="monetary" options="{'currency_field': 'currency_id'}" class="text-info"/>
                                    </group>
                                    <group string="Profit &amp; Loss">
                                        <field name="net_profit_loss" widget="monetary"/>
                                        <separator/>
                                        <div class="alert alert-info" role="status" invisible="pl_transfer_mode != 'automatic'">
                                            <i class="fa fa-info-circle" title="Information"/> P&amp;L will be transferred automatically
                                        </div>
                                        <div class="alert alert-warning" role="alert" invisible="pl_transfer_mode != 'manual'">
                                            <i class="fa fa-exclamation-triangle" title="Warning"/> P&amp;L transfer requires manual action
                                        </div>
                                    </group>
                                </group>
                            </page>
                            
                            <page string="Report Lines" name="report_lines">
                                <field name="report_lines">
                                    <list string="Report Lines" editable="bottom" decoration-info="section == 'assets'"
                                          decoration-warning="section == 'liabilities'" decoration-success="section == 'equity'"
                                          decoration-primary="section == 'income'" decoration-danger="section == 'expenses'">
                                        <field name="sequence" widget="handle"/>
                                        <field name="section" widget="badge"/>
                                        <field name="name"/>
                                        <field name="opening_balance" widget="monetary" sum="Total Opening"/>
                                        <field name="period_balance" widget="monetary" sum="Total Period"/>
                                        <field name="balance" widget="monetary" sum="Total Closing"/>
                                    </list>
                                </field>
                            </page>
                            
                            <page string="Balance Sheet Data" name="balance_sheet" invisible="not balance_sheet_data">
                                <field name="balance_sheet_data" widget="text" readonly="1"/>
                            </page>

                            <page string="P&amp;L Data" name="profit_loss" invisible="not profit_loss_data">
                                <field name="profit_loss_data" widget="text" readonly="1"/>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>
        
        <!-- Final Account Report List View -->
        <record id="view_account_final_report_list" model="ir.ui.view">
            <field name="name">account.final.report.list</field>
            <field name="model">account.final.report</field>
            <field name="arch" type="xml">
                <list string="Final Account Reports">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <field name="total_assets" widget="monetary"/>
                    <field name="net_profit_loss" widget="monetary"/>
                    <field name="state" widget="badge" decoration-info="state == 'draft'" 
                           decoration-success="state == 'generated'" decoration-primary="state == 'exported'"/>
                </list>
            </field>
        </record>
        
        <!-- Final Account Report Search View -->
        <record id="view_account_final_report_search" model="ir.ui.view">
            <field name="name">account.final.report.search</field>
            <field name="model">account.final.report</field>
            <field name="arch" type="xml">
                <search string="Final Account Reports">
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Generated" name="generated" domain="[('state', '=', 'generated')]"/>
                    <filter string="Exported" name="exported" domain="[('state', '=', 'exported')]"/>
                    <separator/>
                    <filter string="Current Year" name="current_year" 
                            domain="[('date_from', '&gt;=', datetime.datetime.now().strftime('%Y-01-01')),
                                     ('date_to', '&lt;=', datetime.datetime.now().strftime('%Y-12-31'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Company" name="group_company" domain="[]" context="{'group_by': 'company_id'}"/>
                        <filter string="Status" name="group_state" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Date From" name="group_date_from" domain="[]" context="{'group_by': 'date_from'}"/>
                    </group>
                </search>
            </field>
        </record>
        
    </data>
</odoo>