<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Final Account Report Action -->
        <record id="action_account_final_report" model="ir.actions.act_window">
            <field name="name">Final Account Reports</field>
            <field name="res_model">account.final.report</field>
            <field name="view_mode">list,form</field>
            <field name="context">{}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Final Account Report!
                </p>
                <p>
                    Generate Balance Sheet and Profit &amp; Loss reports according to Indian Schedule 3 format.
                    The reports support dynamic date ranges, multi-company operations, and Excel export functionality.
                </p>
            </field>
        </record>
        
        <!-- Final Account Report Wizard Action -->
        <record id="action_final_account_report_wizard" model="ir.actions.act_window">
            <field name="name">Generate Final Account Report</field>
            <field name="res_model">final.account.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{}</field>
        </record>
        
        <!-- Menu Items -->
        <menuitem id="menu_final_account_reports_root" 
                  name="Final Account Reports" 
                  parent="account.menu_finance_reports" 
                  sequence="50"/>
        
        <menuitem id="menu_final_account_reports" 
                  name="Final Account Reports" 
                  parent="menu_final_account_reports_root" 
                  action="action_account_final_report" 
                  sequence="10"/>
        
        <menuitem id="menu_generate_final_account_report"
                  name="Generate Report"
                  parent="menu_final_account_reports_root"
                  action="action_final_account_report_wizard"
                  sequence="20"/>

        <menuitem id="menu_final_account_dashboard"
                  name="Dashboard"
                  parent="menu_final_account_reports_root"
                  action="action_final_account_dashboard"
                  sequence="30"/>

    </data>
</odoo>