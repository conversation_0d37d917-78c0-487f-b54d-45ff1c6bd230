/** @odoo-module **/

import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

const finalAccountDashboardService = {
    dependencies: ["orm", "notification"],

    start(env, { orm, notification }) {
        return {
            async loadReportData(reportId) {
                try {
                    const result = await orm.call(
                        "account.final.report",
                        "read",
                        [reportId, ["balance_sheet_data", "profit_loss_data", "company_id", "date_from", "date_to"]]
                    );
                    return result[0];
                } catch (error) {
                    notification.add(_t("Failed to load report data"), {
                        type: "danger",
                    });
                    return null;
                }
            },
            
            async exportToExcel(reportId) {
                try {
                    const result = await orm.call(
                        "final.account.excel.export",
                        "export_to_excel",
                        [reportId]
                    );
                    
                    if (result && result.url) {
                        window.open(result.url, '_blank');
                        notification.add(_t("Excel export completed successfully"), {
                            type: "success",
                        });
                    }
                    return result;
                } catch (error) {
                    notification.add(_t("Excel export failed: ") + error.message, {
                        type: "danger",
                    });
                    return null;
                }
            },
            
            formatCurrency(amount, currencyId) {
                // Basic currency formatting - can be enhanced
                return new Intl.NumberFormat('en-IN', {
                    style: 'currency',
                    currency: 'INR',
                    minimumFractionDigits: 2
                }).format(amount);
            },
            
            populateDashboardData(dashboardData, reportData) {
                if (!reportData || !dashboardData) return dashboardData;
                
                try {
                    const balanceSheetData = JSON.parse(reportData.balance_sheet_data || '{}');
                    const profitLossData = JSON.parse(reportData.profit_loss_data || '{}');
                    
                    // Update dashboard cells with actual data
                    const sheets = dashboardData.sheets || [];
                    
                    sheets.forEach(sheet => {
                        if (sheet.id === 'final-account-balance-sheet') {
                            this._populateBalanceSheetData(sheet, balanceSheetData, reportData);
                        } else if (sheet.id === 'final-account-profit-loss') {
                            this._populateProfitLossData(sheet, profitLossData, reportData);
                        }
                    });
                    
                    return dashboardData;
                } catch (error) {
                    console.error('Error populating dashboard data:', error);
                    return dashboardData;
                }
            },
            
            _populateBalanceSheetData(sheet, balanceSheetData, reportData) {
                const cells = sheet.cells || {};
                
                // Update company name and date
                if (cells['A1']) {
                    cells['A1'].content = balanceSheetData.company_name || reportData.company_id[1];
                }
                if (cells['A3']) {
                    cells['A3'].content = `As at ${reportData.date_to}`;
                }
                
                // Populate asset data
                const currentAssets = balanceSheetData.assets?.current_assets?.items || [];
                const nonCurrentAssets = balanceSheetData.assets?.non_current_assets?.items || [];
                
                let row = 9;
                currentAssets.forEach(item => {
                    const cellRef = `A${row}`;
                    if (cells[cellRef]) {
                        cells[cellRef].content = `  ${item.name}`;
                        cells[`D${row}`] = { content: item.closing_balance };
                    }
                    row++;
                });
                
                // Add total assets
                if (balanceSheetData.assets?.total_assets) {
                    cells['D17'] = { content: balanceSheetData.assets.total_assets };
                }
            },
            
            _populateProfitLossData(sheet, profitLossData, reportData) {
                const cells = sheet.cells || {};
                
                // Update period
                if (cells['A2']) {
                    cells['A2'].content = `For the period ${reportData.date_from} to ${reportData.date_to}`;
                }
                
                // Populate revenue data
                const revenueItems = profitLossData.revenue?.items || [];
                let row = 8;
                revenueItems.forEach(item => {
                    const cellRef = `A${row}`;
                    if (cells[cellRef]) {
                        cells[cellRef].content = `  ${item.name}`;
                        cells[`D${row}`] = { content: item.closing_balance };
                    }
                    row++;
                });
                
                // Add net profit/loss
                if (profitLossData.net_profit_loss !== undefined) {
                    cells['D22'] = { content: profitLossData.net_profit_loss };
                }
            }
        };
    },
};

registry.category("services").add("final_account_dashboard", finalAccountDashboardService);