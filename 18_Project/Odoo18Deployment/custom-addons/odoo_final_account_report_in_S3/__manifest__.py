# -*- coding: utf-8 -*-
{
    'name': 'Indian Final Account Report - Schedule 3',
    'version': '********.0',
    'category': 'Accounting/Reporting',
    'summary': 'Generate Balance Sheet and Profit & Loss reports according to Indian Schedule 3 format',
    'description': """
Indian Final Account Report - Schedule 3
========================================

This module provides comprehensive financial reporting for Indian companies following Schedule 3 format:

Features:
---------
* Balance Sheet report in Schedule 3 format for Private Limited companies
* Profit & Loss statement with Indian accounting standards
* Dynamic opening balance calculation from any start date
* Posted/Unposted entry filtering options
* Multi-company support with separate chart of accounts
* Excel export using spreadsheet dashboard framework
* Manual/Automatic P&L transfer to Balance Sheet
* Scalable design for future Limited Company format

Technical Features:
------------------
* Integration with existing Odoo accounting framework
* Utilizes native debit/credit/balance calculations
* Multi-company data isolation
* Dashboard integration for interactive reports
* Excel template-based export functionality
    """,
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'depends': [
        'account',
        'spreadsheet_dashboard',
        'spreadsheet_dashboard_account',
        'mail',
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        'security/security.xml',

        # Wizard
        'wizard/final_account_report_wizard_views.xml',

        # Data
        'data/dashboard_data.xml',

        # Views
        'views/account_final_report_views.xml',
        'views/account_final_report_menu_views.xml'
    ],
    'assets': {
        'web.assets_backend': [
            'odoo_final_account_report_in_S3/static/src/js/final_account_dashboard.js',
        ],
    },
    'demo': [],
    'external_dependencies': {
        'python': ['xlsxwriter'],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}