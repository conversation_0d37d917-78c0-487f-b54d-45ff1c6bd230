# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date
import json
import logging

_logger = logging.getLogger(__name__)


class AccountFinalReport(models.Model):
    _name = 'account.final.report'
    _description = 'Indian Final Account Report - Schedule 3'
    _order = 'date_from desc, id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char(string='Report Name', required=True, default=lambda self: _('Final Account Report'))
    company_id = fields.Many2one('res.company', string='Company', required=True, 
                                default=lambda self: self.env.company)
    date_from = fields.Date(string='Start Date', required=True,
                           default=lambda self: fields.Date.today().replace(month=4, day=1))
    date_to = fields.Date(string='End Date', required=True,
                         default=lambda self: fields.Date.today())
    include_unposted = fields.Boolean(string='Include Unposted Entries', default=False)
    pl_transfer_mode = fields.Selection([
        ('manual', 'Manual Transfer'),
        ('automatic', 'Automatic Transfer')
    ], string='P&L Transfer Mode', default='manual', required=True)
    
    # Report Data
    balance_sheet_data = fields.Text(string='Balance Sheet Data', readonly=True)
    profit_loss_data = fields.Text(string='Profit & Loss Data', readonly=True)
    report_lines = fields.One2many('account.final.report.line', 'report_id', string='Report Lines')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('exported', 'Exported')
    ], string='Status', default='draft', readonly=True)
    
    # Computed Fields
    total_assets = fields.Monetary(string='Total Assets', compute='_compute_totals', store=True)
    total_liabilities = fields.Monetary(string='Total Liabilities', compute='_compute_totals', store=True)
    total_equity = fields.Monetary(string='Total Equity', compute='_compute_totals', store=True)
    total_income = fields.Monetary(string='Total Income', compute='_compute_totals', store=True)
    total_expenses = fields.Monetary(string='Total Expenses', compute='_compute_totals', store=True)
    net_profit_loss = fields.Monetary(string='Net Profit/Loss', compute='_compute_totals', store=True)
    balance_check = fields.Monetary(string='Balance Check', compute='_compute_totals', store=True,
                                  help='Verification that Assets = Liabilities + Equity (should be zero)')
    currency_id = fields.Many2one('res.currency', related='company_id.currency_id', readonly=True)

    @api.depends('report_lines.balance')
    def _compute_totals(self):
        for report in self:
            lines = report.report_lines
            report.total_assets = sum(lines.filtered(lambda l: l.account_type.startswith('asset')).mapped('balance'))
            report.total_liabilities = sum(lines.filtered(lambda l: l.account_type.startswith('liability')).mapped('balance'))
            report.total_equity = sum(lines.filtered(lambda l: l.account_type.startswith('equity')).mapped('balance'))

            income_lines = lines.filtered(lambda l: l.account_type.startswith('income'))
            expense_lines = lines.filtered(lambda l: l.account_type.startswith('expense'))
            report.total_income = sum(income_lines.mapped('balance'))
            report.total_expenses = sum(expense_lines.mapped('balance'))
            report.net_profit_loss = report.total_income - report.total_expenses
            if report.total_liabilities < 0:
                report.total_liabilities = abs(report.total_liabilities)
            if report.total_equity < 0:
                report.total_equity = abs(report.total_equity)
            # Balance Check: Assets = Liabilities + Equity
            if report.total_assets < 0:
                report.total_assets = abs(report.total_assets)
            if report.total_equity < 0:
                report.total_equity = abs(report.total_equity)
            if report.total_liabilities < 0:
                report.total_liabilities = abs(report.total_liabilities)
            # Calculate balance check
            report.balance_check = report.total_assets - (report.total_liabilities + report.total_equity)

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for report in self:
            if report.date_from > report.date_to:
                raise UserError(_('Start Date cannot be later than End Date.'))

    def action_generate_report(self):
        """Generate the final account report data"""
        self.ensure_one()
        
        # Clear existing lines
        self.report_lines.unlink()
        
        # Generate report lines
        self._generate_report_lines()
        
        # Generate JSON data for dashboard
        self._generate_json_data()
        
        self.state = 'generated'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def _generate_report_lines(self):
        """Generate report lines with balance calculations"""
        self._generate_balance_sheet_lines()
        self._generate_profit_loss_lines()
        
    def _generate_balance_sheet_lines(self):
        """Generate Balance Sheet lines"""
        bs_types = {
            # Assets
            'asset_receivable': ('Receivable', 10),
            'asset_cash': ('Bank and Cash', 20),
            'asset_current': ('Current Assets', 30),
            'asset_non_current': ('Non-current Assets', 40),
            'asset_prepayments': ('Prepayments', 50),
            'asset_fixed': ('Fixed Assets', 60),
            
            # Liabilities
            'liability_payable': ('Payable', 110),
            'liability_credit_card': ('Credit Card', 120),
            'liability_current': ('Current Liabilities', 130),
            'liability_non_current': ('Non-current Liabilities', 140),
            
            # Equity
            'equity': ('Equity', 210),
            'equity_unaffected': ('Current Year Earnings', 220),
        }
        
        self._create_report_lines(bs_types)
        
    def _generate_profit_loss_lines(self):
        """Generate Profit & Loss lines"""
        pl_types = {
            # Income
            'income': ('Income', 310),
            'income_other': ('Other Income', 320),
            
            # Expenses
            'expense': ('Expenses', 410),
            'expense_depreciation': ('Depreciation', 420),
            'expense_direct_cost': ('Cost of Revenue', 430),
        }
        
        self._create_report_lines(pl_types)
        
    def _create_report_lines(self, account_types_dict):
        """Create report lines for given account types"""
        for account_type, (display_name, sequence) in account_types_dict.items():
            accounts = self.env['account.account'].search([
                ('account_type', '=', account_type),
                ('company_ids', 'in', self.company_id.ids)
            ])
            
            if not accounts:
                continue
                
            # Calculate balances
            opening_balance = self._calculate_opening_balance(accounts.ids)
            period_balance = self._calculate_period_balance(accounts.ids)
            closing_balance = opening_balance + period_balance
            
            # Create line even with zero balance for complete report structure
            self.env['account.final.report.line'].create({
                'report_id': self.id,
                'sequence': sequence,
                'account_type': account_type,
                'name': display_name,
                'opening_balance': opening_balance,
                'period_balance': period_balance,
                'balance': closing_balance,
            })

    def _calculate_opening_balance(self, account_ids):
        """Calculate opening balance before start date using optimized query"""
        if not account_ids:
            return 0.0
            
        domain = [
            ('account_id', 'in', account_ids),
            ('company_id', '=', self.company_id.id),
            ('date', '<', self.date_from),
        ]
        
        if not self.include_unposted:
            domain.append(('parent_state', '=', 'posted'))
        
        # Use read_group for better performance
        result = self.env['account.move.line'].read_group(
            domain=domain,
            fields=['balance:sum'],
            groupby=[]
        )
        return result[0]['balance'] if result else 0.0

    def _calculate_period_balance(self, account_ids):
        """Calculate balance for the period using optimized query"""
        if not account_ids:
            return 0.0
            
        domain = [
            ('account_id', 'in', account_ids),
            ('company_id', '=', self.company_id.id),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
        ]
        
        if not self.include_unposted:
            domain.append(('parent_state', '=', 'posted'))
        
        # Use read_group for better performance
        result = self.env['account.move.line'].read_group(
            domain=domain,
            fields=['balance:sum'],
            groupby=[]
        )
        return result[0]['balance'] if result else 0.0

    def _get_account_type_mapping(self):
        """Get account type mapping for Schedule 3 format with sections"""
        return {
            'balance_sheet': {
                'assets': {
                    'asset_receivable': 'Receivable',
                    'asset_cash': 'Bank and Cash',
                    'asset_current': 'Current Assets',
                    'asset_non_current': 'Non-current Assets',
                    'asset_prepayments': 'Prepayments',
                    'asset_fixed': 'Fixed Assets',
                },
                'liabilities': {
                    'liability_payable': 'Payable',
                    'liability_credit_card': 'Credit Card',
                    'liability_current': 'Current Liabilities',
                    'liability_non_current': 'Non-current Liabilities',
                },
                'equity': {
                    'equity': 'Equity',
                    'equity_unaffected': 'Current Year Earnings',
                }
            },
            'profit_loss': {
                'income': {
                    'income': 'Income',
                    'income_other': 'Other Income',
                },
                'expenses': {
                    'expense': 'Expenses',
                    'expense_depreciation': 'Depreciation',
                    'expense_direct_cost': 'Cost of Revenue',
                }
            }
        }

    def _generate_json_data(self):
        """Generate JSON data for Balance Sheet and P&L in Schedule 3 format"""
        lines = self.report_lines
        
        # Balance Sheet Data - Schedule 3 Format
        balance_sheet = {
            'company_name': self.company_id.name,
            'period': f"{self.date_from.strftime('%d/%m/%Y')} to {self.date_to.strftime('%d/%m/%Y')}",
            'assets': {
                'current_assets': {
                    'items': self._get_section_data(lines, ['asset_current', 'asset_cash', 'asset_receivable']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['asset_current', 'asset_cash', 'asset_receivable']).mapped('balance'))
                },
                'non_current_assets': {
                    'items': self._get_section_data(lines, ['asset_non_current', 'asset_fixed', 'asset_prepayments']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['asset_non_current', 'asset_fixed', 'asset_prepayments']).mapped('balance'))
                },
                'total_assets': self.total_assets
            },
            'liabilities_and_equity': {
                'current_liabilities': {
                    'items': self._get_section_data(lines, ['liability_current', 'liability_payable', 'liability_credit_card']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['liability_current', 'liability_payable', 'liability_credit_card']).mapped('balance'))
                },
                'non_current_liabilities': {
                    'items': self._get_section_data(lines, ['liability_non_current']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['liability_non_current']).mapped('balance'))
                },
                'equity': {
                    'items': self._get_section_data(lines, ['equity', 'equity_unaffected']),
                    'total': self.total_equity
                },
                'total_liabilities_equity': self.total_liabilities + self.total_equity
            }
        }
        
        # P&L Data - Schedule 3 Format
        profit_loss = {
            'company_name': self.company_id.name,
            'period': f"{self.date_from.strftime('%d/%m/%Y')} to {self.date_to.strftime('%d/%m/%Y')}",
            'revenue': {
                'items': self._get_section_data(lines, ['income', 'income_other']),
                'total': sum(lines.filtered(lambda l: l.account_type in ['income', 'income_other']).mapped('balance'))
            },
            'expenses': {
                'items': self._get_section_data(lines, ['expense', 'expense_depreciation', 'expense_direct_cost']),
                'total': sum(lines.filtered(lambda l: l.account_type in ['expense', 'expense_depreciation', 'expense_direct_cost']).mapped('balance'))
            },
            'net_profit_loss': self.net_profit_loss
        }
        
        self.balance_sheet_data = json.dumps(balance_sheet, indent=2)
        self.profit_loss_data = json.dumps(profit_loss, indent=2)

    def _get_section_data(self, lines, account_types):
        """Get data for a specific section with proper formatting"""
        section_lines = lines.filtered(lambda l: l.account_type in account_types).sorted('sequence')
        return [{
            'account_type': line.account_type,
            'name': line.name,
            'opening_balance': float(line.opening_balance),
            'period_balance': float(line.period_balance),
            'closing_balance': float(line.balance),
            'formatted_balance': self.currency_id.format(line.balance) if self.currency_id else str(line.balance)
        } for line in section_lines]

    def action_export_excel(self):
        """Export report to Excel format"""
        self.ensure_one()
        
        if self.state != 'generated':
            raise UserError(_('Please generate the report first.'))
        
        # Use Excel export handler
        excel_export = self.env['final.account.excel.export']
        return excel_export.export_to_excel(self.id)

    def action_transfer_pl(self):
        """Transfer P&L to Balance Sheet"""
        self.ensure_one()
        
        if self.state != 'generated':
            raise UserError(_('Please generate the report first.'))
        
        if self.pl_transfer_mode == 'automatic':
            return self._transfer_pl_automatic()
        else:
            return self._transfer_pl_manual()
    
    def _transfer_pl_automatic(self):
        """Automatic P&L transfer to retained earnings"""
        if abs(self.net_profit_loss) < 0.01:  # Skip if negligible amount
            return True
            
        # Find or create retained earnings line
        retained_earnings_line = self.report_lines.filtered(
            lambda l: l.account_type == 'equity_unaffected'
        )
        
        if retained_earnings_line:
            # Update existing retained earnings with P&L
            retained_earnings_line.balance += self.net_profit_loss
            retained_earnings_line.period_balance += self.net_profit_loss
        else:
            # Create new retained earnings line
            self.env['account.final.report.line'].create({
                'report_id': self.id,
                'sequence': 220,
                'account_type': 'equity_unaffected',
                'name': 'Current Year Earnings',
                'opening_balance': 0.0,
                'period_balance': self.net_profit_loss,
                'balance': self.net_profit_loss,
            })
        
        # Regenerate JSON data
        self._generate_json_data()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': _('P&L transferred automatically to retained earnings.'),
                'type': 'success',
                'sticky': False,
            }
        }
    
    def _transfer_pl_manual(self):
        """Manual P&L transfer - show confirmation dialog"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Transfer P&L'),
            'res_model': 'account.final.report',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'dialog_size': 'medium',
                'show_pl_transfer_dialog': True,
                'default_net_profit_loss': self.net_profit_loss,
            }
        }
    
    def validate_report_data(self):
        """Validate report data for accuracy"""
        self.ensure_one()
        
        errors = []
        warnings = []
        
        # Check Balance Sheet equation: Assets = Liabilities + Equity
        balance_difference = abs(self.total_assets - (abs(self.total_liabilities) + abs(self.total_equity)))
        if balance_difference > 0.01:
            # Log detailed breakdown for debugging
            _logger.info('Balance Sheet Validation Details:')
            _logger.info('Total Assets: %s', self.total_assets)
            _logger.info('Total Liabilities: %s', self.total_liabilities)
            _logger.info('Total Equity: %s', self.total_equity)
            _logger.info('Calculated Difference: %s', balance_difference)
            
            # Log individual account balances
            for line in self.report_lines.sorted('sequence'):
                _logger.info('%s (%s): %s', line.name, line.account_type, line.balance)
            
            errors.append(_('Balance Sheet does not balance. Difference: %s') %
                         self.currency_id.format(balance_difference))
        
        # Check for missing account types (only warn if accounts exist but not in report)
        existing_types = set(self.report_lines.mapped('account_type'))
        expected_types = set(self._get_all_account_types())
        missing_types = expected_types - existing_types
        
        # Only warn if company actually has accounts of these types
        if missing_types:
            account_counts = self.env['account.account'].search_count([
                ('account_type', 'in', list(missing_types)),
                ('company_ids', 'in', self.company_id.ids)
            ])
            if account_counts > 0:
                warnings.append(_('Missing account types: %s') % ', '.join(missing_types))
        
        # Check date range validity
        if self.date_from > self.date_to:
            errors.append(_('Start date cannot be after end date.'))
        # Write chatter with validation results
        if errors or warnings:
            message = ''
            # Adding Balance Sheet Validation Details
            if balance_difference > 0.01:
                message += _('Balance Sheet Validation:\n')
                message += _('Total Assets: %s\n') % self.currency_id.format(self.total_assets)
                message += _('Total Liabilities: %s\n') % self.currency_id.format(self.total_liabilities)
                message += _('Total Equity: %s\n') % self.currency_id.format(self.total_equity)
                message += _('Calculated Difference: %s\n') % self.currency_id.format(balance_difference)
            if errors:
                message += _('Errors:\n') + '\n'.join(errors) + '\n\n'
            if warnings:
                message += _('Warnings:\n') + '\n'.join(warnings)
            self.message_post(body=message)
    
    def _get_all_account_types(self):
        """Get all expected account types for validation"""
        mapping = self._get_account_type_mapping()
        types = []
        for section in mapping.values():
            for subsection in section.values():
                types.extend(subsection.keys())
        return types

    @api.model
    def create_sample_report(self):
        """Create a sample report with demo data for dashboard testing"""
        # Create sample report
        report = self.env['account.final.report'].create({
            'name': 'Sample Final Account Report - Dashboard Test',
            'company_id': self.env.company.id,
            'date_from': fields.Date.today().replace(month=4, day=1),
            'date_to': fields.Date.today(),
            'state': 'generated',
        })

        # Create sample report lines
        sample_lines = [
            # Assets
            {'name': 'Accounts Receivable', 'section': 'assets', 'account_type': 'asset_receivable',
             'opening_balance': 50000, 'period_balance': 15000, 'balance': 65000, 'sequence': 1},
            {'name': 'Bank and Cash', 'section': 'assets', 'account_type': 'asset_cash',
             'opening_balance': 25000, 'period_balance': 5000, 'balance': 30000, 'sequence': 2},
            {'name': 'Current Assets', 'section': 'assets', 'account_type': 'asset_current',
             'opening_balance': 30000, 'period_balance': 8000, 'balance': 38000, 'sequence': 3},
            {'name': 'Fixed Assets', 'section': 'assets', 'account_type': 'asset_fixed',
             'opening_balance': 100000, 'period_balance': -5000, 'balance': 95000, 'sequence': 4},
            {'name': 'Non-current Assets', 'section': 'assets', 'account_type': 'asset_non_current',
             'opening_balance': 20000, 'period_balance': 2000, 'balance': 22000, 'sequence': 5},

            # Liabilities
            {'name': 'Accounts Payable', 'section': 'liabilities', 'account_type': 'liability_payable',
             'opening_balance': 35000, 'period_balance': 8000, 'balance': 43000, 'sequence': 6},
            {'name': 'Current Liabilities', 'section': 'liabilities', 'account_type': 'liability_current',
             'opening_balance': 15000, 'period_balance': 3000, 'balance': 18000, 'sequence': 7},

            # Equity
            {'name': 'Share Capital', 'section': 'equity', 'account_type': 'equity',
             'opening_balance': 150000, 'period_balance': 0, 'balance': 150000, 'sequence': 8},
            {'name': 'Retained Earnings', 'section': 'equity', 'account_type': 'equity_unaffected',
             'opening_balance': 40000, 'period_balance': 25000, 'balance': 65000, 'sequence': 9},

            # Income
            {'name': 'Sales Revenue', 'section': 'income', 'account_type': 'income',
             'opening_balance': 0, 'period_balance': 80000, 'balance': 80000, 'sequence': 10},
            {'name': 'Other Income', 'section': 'income', 'account_type': 'income_other',
             'opening_balance': 0, 'period_balance': 5000, 'balance': 5000, 'sequence': 11},

            # Expenses
            {'name': 'Operating Expenses', 'section': 'expenses', 'account_type': 'expense',
             'opening_balance': 0, 'period_balance': 45000, 'balance': 45000, 'sequence': 12},
            {'name': 'Depreciation', 'section': 'expenses', 'account_type': 'expense_depreciation',
             'opening_balance': 0, 'period_balance': 5000, 'balance': 5000, 'sequence': 13},
            {'name': 'Cost of Goods Sold', 'section': 'expenses', 'account_type': 'expense_direct_cost',
             'opening_balance': 0, 'period_balance': 30000, 'balance': 30000, 'sequence': 14},
        ]

        for line_data in sample_lines:
            line_data['report_id'] = report.id
            self.env['account.final.report.line'].create(line_data)

        # Trigger computation of totals
        report._compute_totals()

        return report